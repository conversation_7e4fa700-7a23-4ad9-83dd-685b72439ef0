<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis simplifyMaxScale="1" minScale="100000000" readOnly="0" maxScale="0" labelsEnabled="1" hasScaleBasedVisibilityFlag="0" simplifyDrawingTol="1" simplifyLocal="1" simplifyDrawingHints="1" simplifyAlgorithm="0" symbologyReferenceScale="-1" styleCategories="AllStyleCategories" version="3.36.1-Maidenhead">
  <flags>
    <Identifiable>1</Identifiable>
    <Removable>1</Removable>
    <Searchable>1</Searchable>
    <Private>0</Private>
  </flags>
  <temporal startExpression="" endExpression="" accumulate="0" durationField="fid" durationUnit="min" mode="0" endField="" enabled="0" startField="timestamp" fixedDuration="0" limitMode="0">
    <fixedRange>
      <start></start>
      <end></end>
    </fixedRange>
  </temporal>
  <elevation zscale="1" zoffset="0" extrusion="0" showMarkerSymbolInSurfacePlots="0" type="IndividualFeatures" symbology="Line" respectLayerSymbol="1" extrusionEnabled="0" binding="Centroid" clamping="Terrain">
    <data-defined-properties>
      <Option type="Map">
        <Option value="" name="name" type="QString"/>
        <Option name="properties"/>
        <Option value="collection" name="type" type="QString"/>
      </Option>
    </data-defined-properties>
    <profileLineSymbol>
      <symbol force_rhr="0" name="" type="line" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
        </data_defined_properties>
        <layer pass="0" class="SimpleLine" enabled="1" locked="0" id="{995f0e50-c76e-499b-a054-960c7d3d7377}">
          <Option type="Map">
            <Option value="0" name="align_dash_pattern" type="QString"/>
            <Option value="square" name="capstyle" type="QString"/>
            <Option value="5;2" name="customdash" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="customdash_map_unit_scale" type="QString"/>
            <Option value="MM" name="customdash_unit" type="QString"/>
            <Option value="0" name="dash_pattern_offset" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="dash_pattern_offset_map_unit_scale" type="QString"/>
            <Option value="MM" name="dash_pattern_offset_unit" type="QString"/>
            <Option value="0" name="draw_inside_polygon" type="QString"/>
            <Option value="bevel" name="joinstyle" type="QString"/>
            <Option value="231,113,72,255,rgb:0.90588235294117647,0.44313725490196076,0.28235294117647058,1" name="line_color" type="QString"/>
            <Option value="solid" name="line_style" type="QString"/>
            <Option value="0.6" name="line_width" type="QString"/>
            <Option value="MM" name="line_width_unit" type="QString"/>
            <Option value="0" name="offset" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
            <Option value="MM" name="offset_unit" type="QString"/>
            <Option value="0" name="ring_filter" type="QString"/>
            <Option value="0" name="trim_distance_end" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="trim_distance_end_map_unit_scale" type="QString"/>
            <Option value="MM" name="trim_distance_end_unit" type="QString"/>
            <Option value="0" name="trim_distance_start" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="trim_distance_start_map_unit_scale" type="QString"/>
            <Option value="MM" name="trim_distance_start_unit" type="QString"/>
            <Option value="0" name="tweak_dash_pattern_on_corners" type="QString"/>
            <Option value="0" name="use_custom_dash" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="width_map_unit_scale" type="QString"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option value="" name="name" type="QString"/>
              <Option name="properties"/>
              <Option value="collection" name="type" type="QString"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </profileLineSymbol>
    <profileFillSymbol>
      <symbol force_rhr="0" name="" type="fill" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
        </data_defined_properties>
        <layer pass="0" class="SimpleFill" enabled="1" locked="0" id="{0d6b634b-e78e-42eb-9211-a4af880251dc}">
          <Option type="Map">
            <Option value="3x:0,0,0,0,0,0" name="border_width_map_unit_scale" type="QString"/>
            <Option value="231,113,72,255,rgb:0.90588235294117647,0.44313725490196076,0.28235294117647058,1" name="color" type="QString"/>
            <Option value="bevel" name="joinstyle" type="QString"/>
            <Option value="0,0" name="offset" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
            <Option value="MM" name="offset_unit" type="QString"/>
            <Option value="165,81,51,255,rgb:0.6470588235294118,0.31764705882352939,0.20000000000000001,1" name="outline_color" type="QString"/>
            <Option value="solid" name="outline_style" type="QString"/>
            <Option value="0.2" name="outline_width" type="QString"/>
            <Option value="MM" name="outline_width_unit" type="QString"/>
            <Option value="solid" name="style" type="QString"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option value="" name="name" type="QString"/>
              <Option name="properties"/>
              <Option value="collection" name="type" type="QString"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </profileFillSymbol>
    <profileMarkerSymbol>
      <symbol force_rhr="0" name="" type="marker" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
        </data_defined_properties>
        <layer pass="0" class="SimpleMarker" enabled="1" locked="0" id="{a8bf004f-eab1-4556-9ae9-3a1b25746e07}">
          <Option type="Map">
            <Option value="0" name="angle" type="QString"/>
            <Option value="square" name="cap_style" type="QString"/>
            <Option value="231,113,72,255,rgb:0.90588235294117647,0.44313725490196076,0.28235294117647058,1" name="color" type="QString"/>
            <Option value="1" name="horizontal_anchor_point" type="QString"/>
            <Option value="bevel" name="joinstyle" type="QString"/>
            <Option value="diamond" name="name" type="QString"/>
            <Option value="0,0" name="offset" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
            <Option value="MM" name="offset_unit" type="QString"/>
            <Option value="165,81,51,255,rgb:0.6470588235294118,0.31764705882352939,0.20000000000000001,1" name="outline_color" type="QString"/>
            <Option value="solid" name="outline_style" type="QString"/>
            <Option value="0.2" name="outline_width" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="outline_width_map_unit_scale" type="QString"/>
            <Option value="MM" name="outline_width_unit" type="QString"/>
            <Option value="diameter" name="scale_method" type="QString"/>
            <Option value="3" name="size" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="size_map_unit_scale" type="QString"/>
            <Option value="MM" name="size_unit" type="QString"/>
            <Option value="1" name="vertical_anchor_point" type="QString"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option value="" name="name" type="QString"/>
              <Option name="properties"/>
              <Option value="collection" name="type" type="QString"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </profileMarkerSymbol>
  </elevation>
  <renderer-v2 symbollevels="0" forceraster="0" type="singleSymbol" enableorderby="0" referencescale="-1">
    <symbols>
      <symbol force_rhr="0" name="0" type="fill" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
        </data_defined_properties>
        <layer pass="0" class="SimpleFill" enabled="1" locked="0" id="{4f29f182-77c7-426b-bdbf-cb443e0ffee8}">
          <Option type="Map">
            <Option value="3x:0,0,0,0,0,0" name="border_width_map_unit_scale" type="QString"/>
            <Option value="255,0,200,255,rgb:1,0,0.78431372549019607,1" name="color" type="QString"/>
            <Option value="bevel" name="joinstyle" type="QString"/>
            <Option value="0,0" name="offset" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
            <Option value="MM" name="offset_unit" type="QString"/>
            <Option value="35,35,35,255,rgb:0.13725490196078433,0.13725490196078433,0.13725490196078433,1" name="outline_color" type="QString"/>
            <Option value="no" name="outline_style" type="QString"/>
            <Option value="0.26" name="outline_width" type="QString"/>
            <Option value="MM" name="outline_width_unit" type="QString"/>
            <Option value="solid" name="style" type="QString"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option value="" name="name" type="QString"/>
              <Option name="properties"/>
              <Option value="collection" name="type" type="QString"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </symbols>
    <rotation/>
    <sizescale/>
  </renderer-v2>
  <selection mode="Default">
    <selectionColor invalid="1"/>
    <selectionSymbol>
      <symbol force_rhr="0" name="" type="fill" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
        </data_defined_properties>
        <layer pass="0" class="SimpleFill" enabled="1" locked="0" id="{bdbdc774-5cd9-45ae-8cf4-44f0149e55d4}">
          <Option type="Map">
            <Option value="3x:0,0,0,0,0,0" name="border_width_map_unit_scale" type="QString"/>
            <Option value="0,0,255,255,rgb:0,0,1,1" name="color" type="QString"/>
            <Option value="bevel" name="joinstyle" type="QString"/>
            <Option value="0,0" name="offset" type="QString"/>
            <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
            <Option value="MM" name="offset_unit" type="QString"/>
            <Option value="35,35,35,255,rgb:0.13725490196078433,0.13725490196078433,0.13725490196078433,1" name="outline_color" type="QString"/>
            <Option value="solid" name="outline_style" type="QString"/>
            <Option value="0.26" name="outline_width" type="QString"/>
            <Option value="MM" name="outline_width_unit" type="QString"/>
            <Option value="solid" name="style" type="QString"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option value="" name="name" type="QString"/>
              <Option name="properties"/>
              <Option value="collection" name="type" type="QString"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </selectionSymbol>
  </selection>
  <labeling type="simple">
    <settings calloutType="simple">
      <text-style multilineHeight="1" fontItalic="0" fieldName="Concat('GRB on populated: ', round(sum(&quot;area&quot;),2),' sqKm / ', &quot;areaTot&quot; ,' sqKm (', round(sum(&quot;area&quot;)/&quot;areaTot&quot; * 100,2) ,'%)')&#xd;&#xa;&#xd;&#xa;&#xd;&#xa;" fontSize="20" capitalization="0" fontSizeUnit="Pixel" fontSizeMapUnitScale="3x:0,0,0,0,0,0" fontWeight="75" legendString="Aa" fontLetterSpacing="0" fontFamily="Open Sans" allowHtml="0" isExpression="1" multilineHeightUnit="Percentage" previewBkgrdColor="255,255,255,255,rgb:1,1,1,1" textOrientation="horizontal" blendMode="0" useSubstitutions="0" textColor="0,0,0,255,rgb:0,0,0,1" fontWordSpacing="0" fontKerning="1" fontStrikeout="0" forcedItalic="0" forcedBold="0" textOpacity="1" fontUnderline="0" namedStyle="Bold">
        <families/>
        <text-buffer bufferJoinStyle="128" bufferBlendMode="0" bufferSizeMapUnitScale="3x:0,0,0,0,0,0" bufferNoFill="1" bufferOpacity="1" bufferDraw="1" bufferSize="2" bufferSizeUnits="Pixel" bufferColor="250,250,250,255,rgb:0.98039215686274506,0.98039215686274506,0.98039215686274506,1"/>
        <text-mask maskEnabled="0" maskSizeMapUnitScale="3x:0,0,0,0,0,0" maskedSymbolLayers="" maskOpacity="1" maskSizeUnits="MM" maskType="0" maskSize="0" maskJoinStyle="128"/>
        <background shapeBorderWidth="0" shapeBorderWidthMapUnitScale="3x:0,0,0,0,0,0" shapeOpacity="1" shapeBlendMode="0" shapeOffsetX="0" shapeRadiiUnit="Point" shapeOffsetMapUnitScale="3x:0,0,0,0,0,0" shapeRadiiX="0" shapeSizeY="0" shapeOffsetY="0" shapeRadiiMapUnitScale="3x:0,0,0,0,0,0" shapeType="0" shapeSizeX="0" shapeBorderWidthUnit="Point" shapeOffsetUnit="Point" shapeRadiiY="0" shapeJoinStyle="64" shapeRotationType="0" shapeSizeUnit="Point" shapeSizeMapUnitScale="3x:0,0,0,0,0,0" shapeDraw="0" shapeBorderColor="128,128,128,255,rgb:0.50196078431372548,0.50196078431372548,0.50196078431372548,1" shapeSVGFile="" shapeFillColor="255,255,255,255,rgb:1,1,1,1" shapeRotation="0" shapeSizeType="0">
          <symbol force_rhr="0" name="markerSymbol" type="marker" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
            <data_defined_properties>
              <Option type="Map">
                <Option value="" name="name" type="QString"/>
                <Option name="properties"/>
                <Option value="collection" name="type" type="QString"/>
              </Option>
            </data_defined_properties>
            <layer pass="0" class="SimpleMarker" enabled="1" locked="0" id="">
              <Option type="Map">
                <Option value="0" name="angle" type="QString"/>
                <Option value="square" name="cap_style" type="QString"/>
                <Option value="133,182,111,255,rgb:0.52156862745098043,0.71372549019607845,0.43529411764705883,1" name="color" type="QString"/>
                <Option value="1" name="horizontal_anchor_point" type="QString"/>
                <Option value="bevel" name="joinstyle" type="QString"/>
                <Option value="circle" name="name" type="QString"/>
                <Option value="0,0" name="offset" type="QString"/>
                <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
                <Option value="MM" name="offset_unit" type="QString"/>
                <Option value="35,35,35,255,rgb:0.13725490196078433,0.13725490196078433,0.13725490196078433,1" name="outline_color" type="QString"/>
                <Option value="solid" name="outline_style" type="QString"/>
                <Option value="0" name="outline_width" type="QString"/>
                <Option value="3x:0,0,0,0,0,0" name="outline_width_map_unit_scale" type="QString"/>
                <Option value="MM" name="outline_width_unit" type="QString"/>
                <Option value="diameter" name="scale_method" type="QString"/>
                <Option value="2" name="size" type="QString"/>
                <Option value="3x:0,0,0,0,0,0" name="size_map_unit_scale" type="QString"/>
                <Option value="MM" name="size_unit" type="QString"/>
                <Option value="1" name="vertical_anchor_point" type="QString"/>
              </Option>
              <data_defined_properties>
                <Option type="Map">
                  <Option value="" name="name" type="QString"/>
                  <Option name="properties"/>
                  <Option value="collection" name="type" type="QString"/>
                </Option>
              </data_defined_properties>
            </layer>
          </symbol>
          <symbol force_rhr="0" name="fillSymbol" type="fill" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
            <data_defined_properties>
              <Option type="Map">
                <Option value="" name="name" type="QString"/>
                <Option name="properties"/>
                <Option value="collection" name="type" type="QString"/>
              </Option>
            </data_defined_properties>
            <layer pass="0" class="SimpleFill" enabled="1" locked="0" id="">
              <Option type="Map">
                <Option value="3x:0,0,0,0,0,0" name="border_width_map_unit_scale" type="QString"/>
                <Option value="255,255,255,255,rgb:1,1,1,1" name="color" type="QString"/>
                <Option value="bevel" name="joinstyle" type="QString"/>
                <Option value="0,0" name="offset" type="QString"/>
                <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
                <Option value="MM" name="offset_unit" type="QString"/>
                <Option value="128,128,128,255,rgb:0.50196078431372548,0.50196078431372548,0.50196078431372548,1" name="outline_color" type="QString"/>
                <Option value="no" name="outline_style" type="QString"/>
                <Option value="0" name="outline_width" type="QString"/>
                <Option value="Point" name="outline_width_unit" type="QString"/>
                <Option value="solid" name="style" type="QString"/>
              </Option>
              <data_defined_properties>
                <Option type="Map">
                  <Option value="" name="name" type="QString"/>
                  <Option name="properties"/>
                  <Option value="collection" name="type" type="QString"/>
                </Option>
              </data_defined_properties>
            </layer>
          </symbol>
        </background>
        <shadow shadowOffsetMapUnitScale="3x:0,0,0,0,0,0" shadowDraw="0" shadowOffsetDist="1" shadowRadiusAlphaOnly="0" shadowOffsetGlobal="1" shadowRadius="1.5" shadowBlendMode="6" shadowRadiusUnit="MM" shadowScale="100" shadowOffsetUnit="MM" shadowOffsetAngle="135" shadowRadiusMapUnitScale="3x:0,0,0,0,0,0" shadowUnder="0" shadowOpacity="0.69999999999999996" shadowColor="0,0,0,255,rgb:0,0,0,1"/>
        <dd_properties>
          <Option type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
        </dd_properties>
        <substitutions/>
      </text-style>
      <text-format addDirectionSymbol="0" useMaxLineLengthForAutoWrap="1" multilineAlign="3" leftDirectionSymbol="&lt;" placeDirectionSymbol="0" plussign="0" decimals="3" reverseDirectionSymbol="0" wrapChar="" autoWrapLength="0" rightDirectionSymbol=">" formatNumbers="0"/>
      <placement xOffset="0" geometryGenerator="" yOffset="0" overrunDistance="0" distMapUnitScale="3x:0,0,0,0,0,0" placementFlags="10" centroidWhole="0" lineAnchorTextPoint="FollowPlacement" dist="0" rotationAngle="0" geometryGeneratorType="PointGeometry" fitInPolygonOnly="0" placement="8" overlapHandling="AllowOverlapIfRequired" quadOffset="4" rotationUnit="AngleDegrees" offsetUnits="MM" repeatDistanceUnits="MM" repeatDistanceMapUnitScale="3x:0,0,0,0,0,0" maxCurvedCharAngleOut="-25" lineAnchorClipping="0" lineAnchorType="0" overrunDistanceMapUnitScale="3x:0,0,0,0,0,0" polygonPlacementFlags="2" overrunDistanceUnit="MM" layerType="PolygonGeometry" priority="5" lineAnchorPercent="0.5" offsetType="0" maxCurvedCharAngleIn="25" distUnits="MM" repeatDistance="0" labelOffsetMapUnitScale="3x:0,0,0,0,0,0" preserveRotation="1" predefinedPositionOrder="TR,TL,BR,BL,R,L,TSR,BSR" allowDegraded="0" centroidInside="0" geometryGeneratorEnabled="0"/>
      <rendering scaleMax="0" obstacle="1" labelPerPart="0" fontMinPixelSize="3" fontMaxPixelSize="10000" scaleVisibility="0" fontLimitPixelSize="0" upsidedownLabels="0" minFeatureSize="0" mergeLines="0" obstacleType="1" obstacleFactor="1" scaleMin="0" zIndex="0" limitNumLabels="1" maxNumLabels="1" unplacedVisibility="0" drawLabels="1"/>
      <dd_properties>
        <Option type="Map">
          <Option value="" name="name" type="QString"/>
          <Option name="properties"/>
          <Option value="collection" name="type" type="QString"/>
        </Option>
      </dd_properties>
      <callout type="simple">
        <Option type="Map">
          <Option value="pole_of_inaccessibility" name="anchorPoint" type="QString"/>
          <Option value="0" name="blendMode" type="int"/>
          <Option name="ddProperties" type="Map">
            <Option value="" name="name" type="QString"/>
            <Option name="properties"/>
            <Option value="collection" name="type" type="QString"/>
          </Option>
          <Option value="false" name="drawToAllParts" type="bool"/>
          <Option value="0" name="enabled" type="QString"/>
          <Option value="point_on_exterior" name="labelAnchorPoint" type="QString"/>
          <Option value="&lt;symbol force_rhr=&quot;0&quot; name=&quot;symbol&quot; type=&quot;line&quot; clip_to_extent=&quot;1&quot; is_animated=&quot;0&quot; alpha=&quot;1&quot; frame_rate=&quot;10&quot;>&lt;data_defined_properties>&lt;Option type=&quot;Map&quot;>&lt;Option value=&quot;&quot; name=&quot;name&quot; type=&quot;QString&quot;/>&lt;Option name=&quot;properties&quot;/>&lt;Option value=&quot;collection&quot; name=&quot;type&quot; type=&quot;QString&quot;/>&lt;/Option>&lt;/data_defined_properties>&lt;layer pass=&quot;0&quot; class=&quot;SimpleLine&quot; enabled=&quot;1&quot; locked=&quot;0&quot; id=&quot;{6b60c642-de7c-47d8-b42c-db0b2ff2658b}&quot;>&lt;Option type=&quot;Map&quot;>&lt;Option value=&quot;0&quot; name=&quot;align_dash_pattern&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;square&quot; name=&quot;capstyle&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;5;2&quot; name=&quot;customdash&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;3x:0,0,0,0,0,0&quot; name=&quot;customdash_map_unit_scale&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;MM&quot; name=&quot;customdash_unit&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;dash_pattern_offset&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;3x:0,0,0,0,0,0&quot; name=&quot;dash_pattern_offset_map_unit_scale&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;MM&quot; name=&quot;dash_pattern_offset_unit&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;draw_inside_polygon&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;bevel&quot; name=&quot;joinstyle&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;60,60,60,255,rgb:0.23529411764705882,0.23529411764705882,0.23529411764705882,1&quot; name=&quot;line_color&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;solid&quot; name=&quot;line_style&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0.3&quot; name=&quot;line_width&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;MM&quot; name=&quot;line_width_unit&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;offset&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;3x:0,0,0,0,0,0&quot; name=&quot;offset_map_unit_scale&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;MM&quot; name=&quot;offset_unit&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;ring_filter&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;trim_distance_end&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;3x:0,0,0,0,0,0&quot; name=&quot;trim_distance_end_map_unit_scale&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;MM&quot; name=&quot;trim_distance_end_unit&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;trim_distance_start&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;3x:0,0,0,0,0,0&quot; name=&quot;trim_distance_start_map_unit_scale&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;MM&quot; name=&quot;trim_distance_start_unit&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;tweak_dash_pattern_on_corners&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;0&quot; name=&quot;use_custom_dash&quot; type=&quot;QString&quot;/>&lt;Option value=&quot;3x:0,0,0,0,0,0&quot; name=&quot;width_map_unit_scale&quot; type=&quot;QString&quot;/>&lt;/Option>&lt;data_defined_properties>&lt;Option type=&quot;Map&quot;>&lt;Option value=&quot;&quot; name=&quot;name&quot; type=&quot;QString&quot;/>&lt;Option name=&quot;properties&quot;/>&lt;Option value=&quot;collection&quot; name=&quot;type&quot; type=&quot;QString&quot;/>&lt;/Option>&lt;/data_defined_properties>&lt;/layer>&lt;/symbol>" name="lineSymbol" type="QString"/>
          <Option value="0" name="minLength" type="double"/>
          <Option value="3x:0,0,0,0,0,0" name="minLengthMapUnitScale" type="QString"/>
          <Option value="MM" name="minLengthUnit" type="QString"/>
          <Option value="0" name="offsetFromAnchor" type="double"/>
          <Option value="3x:0,0,0,0,0,0" name="offsetFromAnchorMapUnitScale" type="QString"/>
          <Option value="MM" name="offsetFromAnchorUnit" type="QString"/>
          <Option value="0" name="offsetFromLabel" type="double"/>
          <Option value="3x:0,0,0,0,0,0" name="offsetFromLabelMapUnitScale" type="QString"/>
          <Option value="MM" name="offsetFromLabelUnit" type="QString"/>
        </Option>
      </callout>
    </settings>
  </labeling>
  <customproperties>
    <Option type="Map">
      <Option name="dualview/previewExpressions" type="List">
        <Option value="&quot;fid&quot;" type="QString"/>
      </Option>
      <Option value="0" name="embeddedWidgets/count" type="int"/>
      <Option name="variableNames" type="StringList">
        <Option value="ratio" type="QString"/>
      </Option>
      <Option name="variableValues" type="StringList">
        <Option value="1.4937996871051293" type="QString"/>
      </Option>
    </Option>
  </customproperties>
  <blendMode>0</blendMode>
  <featureBlendMode>0</featureBlendMode>
  <layerOpacity>0.645</layerOpacity>
  <SingleCategoryDiagramRenderer attributeLegend="1" diagramType="Histogram">
    <DiagramCategory penAlpha="255" enabled="0" minimumSize="0" penWidth="0" spacingUnit="MM" lineSizeScale="3x:0,0,0,0,0,0" sizeScale="3x:0,0,0,0,0,0" scaleBasedVisibility="0" backgroundAlpha="255" height="15" opacity="1" minScaleDenominator="0" barWidth="5" spacingUnitScale="3x:0,0,0,0,0,0" maxScaleDenominator="1e+08" lineSizeType="MM" width="15" direction="0" diagramOrientation="Up" penColor="#000000" spacing="5" backgroundColor="#ffffff" labelPlacementMethod="XHeight" scaleDependency="Area" rotationOffset="270" showAxis="1" sizeType="MM">
      <fontProperties bold="0" underline="0" style="" strikethrough="0" italic="0" description="MS Shell Dlg 2,8.25,-1,5,50,0,0,0,0,0"/>
      <attribute label="" color="#000000" field="" colorOpacity="1"/>
      <axisSymbol>
        <symbol force_rhr="0" name="" type="line" clip_to_extent="1" is_animated="0" alpha="1" frame_rate="10">
          <data_defined_properties>
            <Option type="Map">
              <Option value="" name="name" type="QString"/>
              <Option name="properties"/>
              <Option value="collection" name="type" type="QString"/>
            </Option>
          </data_defined_properties>
          <layer pass="0" class="SimpleLine" enabled="1" locked="0" id="{94ab5101-bbbb-43bf-b657-8b9dd4c531e7}">
            <Option type="Map">
              <Option value="0" name="align_dash_pattern" type="QString"/>
              <Option value="square" name="capstyle" type="QString"/>
              <Option value="5;2" name="customdash" type="QString"/>
              <Option value="3x:0,0,0,0,0,0" name="customdash_map_unit_scale" type="QString"/>
              <Option value="MM" name="customdash_unit" type="QString"/>
              <Option value="0" name="dash_pattern_offset" type="QString"/>
              <Option value="3x:0,0,0,0,0,0" name="dash_pattern_offset_map_unit_scale" type="QString"/>
              <Option value="MM" name="dash_pattern_offset_unit" type="QString"/>
              <Option value="0" name="draw_inside_polygon" type="QString"/>
              <Option value="bevel" name="joinstyle" type="QString"/>
              <Option value="35,35,35,255,rgb:0.13725490196078433,0.13725490196078433,0.13725490196078433,1" name="line_color" type="QString"/>
              <Option value="solid" name="line_style" type="QString"/>
              <Option value="0.26" name="line_width" type="QString"/>
              <Option value="MM" name="line_width_unit" type="QString"/>
              <Option value="0" name="offset" type="QString"/>
              <Option value="3x:0,0,0,0,0,0" name="offset_map_unit_scale" type="QString"/>
              <Option value="MM" name="offset_unit" type="QString"/>
              <Option value="0" name="ring_filter" type="QString"/>
              <Option value="0" name="trim_distance_end" type="QString"/>
              <Option value="3x:0,0,0,0,0,0" name="trim_distance_end_map_unit_scale" type="QString"/>
              <Option value="MM" name="trim_distance_end_unit" type="QString"/>
              <Option value="0" name="trim_distance_start" type="QString"/>
              <Option value="3x:0,0,0,0,0,0" name="trim_distance_start_map_unit_scale" type="QString"/>
              <Option value="MM" name="trim_distance_start_unit" type="QString"/>
              <Option value="0" name="tweak_dash_pattern_on_corners" type="QString"/>
              <Option value="0" name="use_custom_dash" type="QString"/>
              <Option value="3x:0,0,0,0,0,0" name="width_map_unit_scale" type="QString"/>
            </Option>
            <data_defined_properties>
              <Option type="Map">
                <Option value="" name="name" type="QString"/>
                <Option name="properties"/>
                <Option value="collection" name="type" type="QString"/>
              </Option>
            </data_defined_properties>
          </layer>
        </symbol>
      </axisSymbol>
    </DiagramCategory>
  </SingleCategoryDiagramRenderer>
  <DiagramLayerSettings obstacle="0" priority="0" placement="1" linePlacementFlags="18" zIndex="0" dist="0" showAll="1">
    <properties>
      <Option type="Map">
        <Option value="" name="name" type="QString"/>
        <Option name="properties"/>
        <Option value="collection" name="type" type="QString"/>
      </Option>
    </properties>
  </DiagramLayerSettings>
  <geometryOptions geometryPrecision="0" removeDuplicateNodes="0">
    <activeChecks/>
    <checkConfiguration type="Map">
      <Option name="QgsGeometryGapCheck" type="Map">
        <Option value="0" name="allowedGapsBuffer" type="double"/>
        <Option value="false" name="allowedGapsEnabled" type="bool"/>
        <Option name="allowedGapsLayer" type="invalid"/>
      </Option>
    </checkConfiguration>
  </geometryOptions>
  <legend type="default-vector" showLabelLegend="0"/>
  <referencedLayers/>
  <fieldConfiguration>
    <field configurationFlags="NoFlag" name="fid">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="VALUE">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="Name">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="description">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="timestamp">
      <editWidget type="DateTime">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="begin">
      <editWidget type="DateTime">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="end">
      <editWidget type="DateTime">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="altitudeMode">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="tessellate">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="extrude">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="visibility">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="drawOrder">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="icon">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="folder">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="type">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="label">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="areaTot">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="id">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="fid_2">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="fg">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="cv">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="grb">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="fmode">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="speed(m/s)">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="altitude">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="deliveries">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="status">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="layer">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="path">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="fgmod">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="cvmod">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="grbmod">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="len">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="lentot">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="ratio">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="display">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="elev_min">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="elev_max">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field configurationFlags="NoFlag" name="area">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
  </fieldConfiguration>
  <aliases>
    <alias index="0" name="" field="fid"/>
    <alias index="1" name="" field="VALUE"/>
    <alias index="2" name="" field="Name"/>
    <alias index="3" name="" field="description"/>
    <alias index="4" name="" field="timestamp"/>
    <alias index="5" name="" field="begin"/>
    <alias index="6" name="" field="end"/>
    <alias index="7" name="" field="altitudeMode"/>
    <alias index="8" name="" field="tessellate"/>
    <alias index="9" name="" field="extrude"/>
    <alias index="10" name="" field="visibility"/>
    <alias index="11" name="" field="drawOrder"/>
    <alias index="12" name="" field="icon"/>
    <alias index="13" name="" field="folder"/>
    <alias index="14" name="" field="type"/>
    <alias index="15" name="" field="label"/>
    <alias index="16" name="" field="areaTot"/>
    <alias index="17" name="" field="id"/>
    <alias index="18" name="" field="fid_2"/>
    <alias index="19" name="" field="fg"/>
    <alias index="20" name="" field="cv"/>
    <alias index="21" name="" field="grb"/>
    <alias index="22" name="" field="fmode"/>
    <alias index="23" name="" field="speed(m/s)"/>
    <alias index="24" name="" field="altitude"/>
    <alias index="25" name="" field="deliveries"/>
    <alias index="26" name="" field="status"/>
    <alias index="27" name="" field="layer"/>
    <alias index="28" name="" field="path"/>
    <alias index="29" name="" field="fgmod"/>
    <alias index="30" name="" field="cvmod"/>
    <alias index="31" name="" field="grbmod"/>
    <alias index="32" name="" field="len"/>
    <alias index="33" name="" field="lentot"/>
    <alias index="34" name="" field="ratio"/>
    <alias index="35" name="" field="display"/>
    <alias index="36" name="" field="elev_min"/>
    <alias index="37" name="" field="elev_max"/>
    <alias index="38" name="" field="area"/>
  </aliases>
  <splitPolicies>
    <policy policy="Duplicate" field="fid"/>
    <policy policy="Duplicate" field="VALUE"/>
    <policy policy="Duplicate" field="Name"/>
    <policy policy="Duplicate" field="description"/>
    <policy policy="Duplicate" field="timestamp"/>
    <policy policy="Duplicate" field="begin"/>
    <policy policy="Duplicate" field="end"/>
    <policy policy="Duplicate" field="altitudeMode"/>
    <policy policy="Duplicate" field="tessellate"/>
    <policy policy="Duplicate" field="extrude"/>
    <policy policy="Duplicate" field="visibility"/>
    <policy policy="Duplicate" field="drawOrder"/>
    <policy policy="Duplicate" field="icon"/>
    <policy policy="Duplicate" field="folder"/>
    <policy policy="Duplicate" field="type"/>
    <policy policy="Duplicate" field="label"/>
    <policy policy="Duplicate" field="areaTot"/>
    <policy policy="Duplicate" field="id"/>
    <policy policy="Duplicate" field="fid_2"/>
    <policy policy="Duplicate" field="fg"/>
    <policy policy="Duplicate" field="cv"/>
    <policy policy="Duplicate" field="grb"/>
    <policy policy="Duplicate" field="fmode"/>
    <policy policy="Duplicate" field="speed(m/s)"/>
    <policy policy="Duplicate" field="altitude"/>
    <policy policy="Duplicate" field="deliveries"/>
    <policy policy="Duplicate" field="status"/>
    <policy policy="Duplicate" field="layer"/>
    <policy policy="Duplicate" field="path"/>
    <policy policy="Duplicate" field="fgmod"/>
    <policy policy="Duplicate" field="cvmod"/>
    <policy policy="Duplicate" field="grbmod"/>
    <policy policy="Duplicate" field="len"/>
    <policy policy="Duplicate" field="lentot"/>
    <policy policy="Duplicate" field="ratio"/>
    <policy policy="Duplicate" field="display"/>
    <policy policy="Duplicate" field="elev_min"/>
    <policy policy="Duplicate" field="elev_max"/>
    <policy policy="Duplicate" field="area"/>
  </splitPolicies>
  <defaults>
    <default expression="" field="fid" applyOnUpdate="0"/>
    <default expression="" field="VALUE" applyOnUpdate="0"/>
    <default expression="" field="Name" applyOnUpdate="0"/>
    <default expression="" field="description" applyOnUpdate="0"/>
    <default expression="" field="timestamp" applyOnUpdate="0"/>
    <default expression="" field="begin" applyOnUpdate="0"/>
    <default expression="" field="end" applyOnUpdate="0"/>
    <default expression="" field="altitudeMode" applyOnUpdate="0"/>
    <default expression="" field="tessellate" applyOnUpdate="0"/>
    <default expression="" field="extrude" applyOnUpdate="0"/>
    <default expression="" field="visibility" applyOnUpdate="0"/>
    <default expression="" field="drawOrder" applyOnUpdate="0"/>
    <default expression="" field="icon" applyOnUpdate="0"/>
    <default expression="" field="folder" applyOnUpdate="0"/>
    <default expression="" field="type" applyOnUpdate="0"/>
    <default expression="" field="label" applyOnUpdate="0"/>
    <default expression="" field="areaTot" applyOnUpdate="0"/>
    <default expression="" field="id" applyOnUpdate="0"/>
    <default expression="" field="fid_2" applyOnUpdate="0"/>
    <default expression="" field="fg" applyOnUpdate="0"/>
    <default expression="" field="cv" applyOnUpdate="0"/>
    <default expression="" field="grb" applyOnUpdate="0"/>
    <default expression="" field="fmode" applyOnUpdate="0"/>
    <default expression="" field="speed(m/s)" applyOnUpdate="0"/>
    <default expression="" field="altitude" applyOnUpdate="0"/>
    <default expression="" field="deliveries" applyOnUpdate="0"/>
    <default expression="" field="status" applyOnUpdate="0"/>
    <default expression="" field="layer" applyOnUpdate="0"/>
    <default expression="" field="path" applyOnUpdate="0"/>
    <default expression="" field="fgmod" applyOnUpdate="0"/>
    <default expression="" field="cvmod" applyOnUpdate="0"/>
    <default expression="" field="grbmod" applyOnUpdate="0"/>
    <default expression="" field="len" applyOnUpdate="0"/>
    <default expression="" field="lentot" applyOnUpdate="0"/>
    <default expression="" field="ratio" applyOnUpdate="0"/>
    <default expression="" field="display" applyOnUpdate="0"/>
    <default expression="" field="elev_min" applyOnUpdate="0"/>
    <default expression="" field="elev_max" applyOnUpdate="0"/>
    <default expression="" field="area" applyOnUpdate="0"/>
  </defaults>
  <constraints>
    <constraint unique_strength="1" notnull_strength="1" constraints="3" exp_strength="0" field="fid"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="VALUE"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="Name"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="description"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="timestamp"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="begin"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="end"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="altitudeMode"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="tessellate"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="extrude"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="visibility"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="drawOrder"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="icon"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="folder"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="type"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="label"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="areaTot"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="id"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="fid_2"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="fg"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="cv"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="grb"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="fmode"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="speed(m/s)"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="altitude"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="deliveries"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="status"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="layer"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="path"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="fgmod"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="cvmod"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="grbmod"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="len"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="lentot"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="ratio"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="display"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="elev_min"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="elev_max"/>
    <constraint unique_strength="0" notnull_strength="0" constraints="0" exp_strength="0" field="area"/>
  </constraints>
  <constraintExpressions>
    <constraint desc="" field="fid" exp=""/>
    <constraint desc="" field="VALUE" exp=""/>
    <constraint desc="" field="Name" exp=""/>
    <constraint desc="" field="description" exp=""/>
    <constraint desc="" field="timestamp" exp=""/>
    <constraint desc="" field="begin" exp=""/>
    <constraint desc="" field="end" exp=""/>
    <constraint desc="" field="altitudeMode" exp=""/>
    <constraint desc="" field="tessellate" exp=""/>
    <constraint desc="" field="extrude" exp=""/>
    <constraint desc="" field="visibility" exp=""/>
    <constraint desc="" field="drawOrder" exp=""/>
    <constraint desc="" field="icon" exp=""/>
    <constraint desc="" field="folder" exp=""/>
    <constraint desc="" field="type" exp=""/>
    <constraint desc="" field="label" exp=""/>
    <constraint desc="" field="areaTot" exp=""/>
    <constraint desc="" field="id" exp=""/>
    <constraint desc="" field="fid_2" exp=""/>
    <constraint desc="" field="fg" exp=""/>
    <constraint desc="" field="cv" exp=""/>
    <constraint desc="" field="grb" exp=""/>
    <constraint desc="" field="fmode" exp=""/>
    <constraint desc="" field="speed(m/s)" exp=""/>
    <constraint desc="" field="altitude" exp=""/>
    <constraint desc="" field="deliveries" exp=""/>
    <constraint desc="" field="status" exp=""/>
    <constraint desc="" field="layer" exp=""/>
    <constraint desc="" field="path" exp=""/>
    <constraint desc="" field="fgmod" exp=""/>
    <constraint desc="" field="cvmod" exp=""/>
    <constraint desc="" field="grbmod" exp=""/>
    <constraint desc="" field="len" exp=""/>
    <constraint desc="" field="lentot" exp=""/>
    <constraint desc="" field="ratio" exp=""/>
    <constraint desc="" field="display" exp=""/>
    <constraint desc="" field="elev_min" exp=""/>
    <constraint desc="" field="elev_max" exp=""/>
    <constraint desc="" field="area" exp=""/>
  </constraintExpressions>
  <expressionfields>
    <field comment="" length="-1" precision="0" name="area" typeName="double precision" type="6" expression=" Round($area / 1e6,2)" subType="0"/>
  </expressionfields>
  <attributeactions>
    <defaultAction key="Canvas" value="{00000000-0000-0000-0000-000000000000}"/>
  </attributeactions>
  <attributetableconfig actionWidgetStyle="dropDown" sortOrder="0" sortExpression="&quot;area&quot;">
    <columns>
      <column name="area" type="field" width="-1" hidden="0"/>
      <column name="fid" type="field" width="-1" hidden="0"/>
      <column name="areaTot" type="field" width="-1" hidden="0"/>
      <column name="VALUE" type="field" width="-1" hidden="0"/>
      <column name="Name" type="field" width="-1" hidden="0"/>
      <column name="description" type="field" width="-1" hidden="0"/>
      <column name="timestamp" type="field" width="-1" hidden="0"/>
      <column name="begin" type="field" width="-1" hidden="0"/>
      <column name="end" type="field" width="-1" hidden="0"/>
      <column name="altitudeMode" type="field" width="-1" hidden="0"/>
      <column name="tessellate" type="field" width="-1" hidden="0"/>
      <column name="extrude" type="field" width="-1" hidden="0"/>
      <column name="visibility" type="field" width="-1" hidden="0"/>
      <column name="drawOrder" type="field" width="-1" hidden="0"/>
      <column name="icon" type="field" width="-1" hidden="0"/>
      <column name="fid_2" type="field" width="-1" hidden="0"/>
      <column name="id" type="field" width="-1" hidden="0"/>
      <column name="fg" type="field" width="-1" hidden="0"/>
      <column name="cv" type="field" width="-1" hidden="0"/>
      <column name="grb" type="field" width="-1" hidden="0"/>
      <column name="speed(m/s)" type="field" width="-1" hidden="0"/>
      <column name="folder" type="field" width="-1" hidden="0"/>
      <column name="altitude" type="field" width="-1" hidden="0"/>
      <column name="deliveries" type="field" width="-1" hidden="0"/>
      <column name="fmode" type="field" width="-1" hidden="0"/>
      <column name="status" type="field" width="-1" hidden="0"/>
      <column name="layer" type="field" width="-1" hidden="0"/>
      <column name="path" type="field" width="-1" hidden="0"/>
      <column name="len" type="field" width="-1" hidden="0"/>
      <column name="ratio" type="field" width="-1" hidden="0"/>
      <column name="display" type="field" width="-1" hidden="0"/>
      <column name="type" type="field" width="-1" hidden="0"/>
      <column name="label" type="field" width="-1" hidden="0"/>
      <column name="elev_min" type="field" width="-1" hidden="0"/>
      <column name="elev_max" type="field" width="-1" hidden="0"/>
      <column name="fgmod" type="field" width="-1" hidden="0"/>
      <column name="cvmod" type="field" width="-1" hidden="0"/>
      <column name="grbmod" type="field" width="-1" hidden="0"/>
      <column name="lentot" type="field" width="-1" hidden="0"/>
      <column type="actions" width="-1" hidden="1"/>
    </columns>
  </attributetableconfig>
  <conditionalstyles>
    <rowstyles/>
    <fieldstyles/>
  </conditionalstyles>
  <storedexpressions/>
  <editform tolerant="1"></editform>
  <editforminit/>
  <editforminitcodesource>0</editforminitcodesource>
  <editforminitfilepath></editforminitfilepath>
  <editforminitcode><![CDATA[# -*- coding: utf-8 -*-
"""
QGIS forms can have a Python function that is called when the form is
opened.

Use this function to add extra logic to your forms.

Enter the name of the function in the "Python Init function"
field.
An example follows:
"""
from qgis.PyQt.QtWidgets import QWidget

def my_form_open(dialog, layer, feature):
    geom = feature.geometry()
    control = dialog.findChild(QWidget, "MyLineEdit")
]]></editforminitcode>
  <featformsuppress>0</featformsuppress>
  <editorlayout>generatedlayout</editorlayout>
  <editable>
    <field editable="1" name="ELEV_MAX"/>
    <field editable="1" name="ELEV_MIN"/>
    <field editable="1" name="ID"/>
    <field editable="1" name="Name"/>
    <field editable="1" name="VALUE"/>
    <field editable="1" name="altitude"/>
    <field editable="1" name="altitudeMode"/>
    <field editable="0" name="area"/>
    <field editable="1" name="areaTot"/>
    <field editable="1" name="begin"/>
    <field editable="1" name="cv"/>
    <field editable="1" name="cvMod"/>
    <field editable="1" name="cvmod"/>
    <field editable="1" name="deliveries"/>
    <field editable="1" name="description"/>
    <field editable="1" name="display"/>
    <field editable="1" name="drawOrder"/>
    <field editable="1" name="elev_max"/>
    <field editable="1" name="elev_min"/>
    <field editable="1" name="end"/>
    <field editable="1" name="extrude"/>
    <field editable="1" name="fg"/>
    <field editable="1" name="fgMod"/>
    <field editable="1" name="fgmod"/>
    <field editable="1" name="fid"/>
    <field editable="1" name="fid_2"/>
    <field editable="1" name="fmode"/>
    <field editable="1" name="folder"/>
    <field editable="1" name="grb"/>
    <field editable="1" name="grbMod"/>
    <field editable="1" name="grbmod"/>
    <field editable="1" name="icon"/>
    <field editable="1" name="id"/>
    <field editable="1" name="label"/>
    <field editable="1" name="layer"/>
    <field editable="1" name="len"/>
    <field editable="1" name="lenTot"/>
    <field editable="1" name="lentot"/>
    <field editable="1" name="path"/>
    <field editable="1" name="ratio"/>
    <field editable="1" name="speed(m/s)"/>
    <field editable="1" name="status"/>
    <field editable="1" name="tessellate"/>
    <field editable="1" name="timestamp"/>
    <field editable="1" name="type"/>
    <field editable="1" name="visibility"/>
  </editable>
  <labelOnTop>
    <field labelOnTop="0" name="ELEV_MAX"/>
    <field labelOnTop="0" name="ELEV_MIN"/>
    <field labelOnTop="0" name="ID"/>
    <field labelOnTop="0" name="Name"/>
    <field labelOnTop="0" name="VALUE"/>
    <field labelOnTop="0" name="altitude"/>
    <field labelOnTop="0" name="altitudeMode"/>
    <field labelOnTop="0" name="area"/>
    <field labelOnTop="0" name="areaTot"/>
    <field labelOnTop="0" name="begin"/>
    <field labelOnTop="0" name="cv"/>
    <field labelOnTop="0" name="cvMod"/>
    <field labelOnTop="0" name="cvmod"/>
    <field labelOnTop="0" name="deliveries"/>
    <field labelOnTop="0" name="description"/>
    <field labelOnTop="0" name="display"/>
    <field labelOnTop="0" name="drawOrder"/>
    <field labelOnTop="0" name="elev_max"/>
    <field labelOnTop="0" name="elev_min"/>
    <field labelOnTop="0" name="end"/>
    <field labelOnTop="0" name="extrude"/>
    <field labelOnTop="0" name="fg"/>
    <field labelOnTop="0" name="fgMod"/>
    <field labelOnTop="0" name="fgmod"/>
    <field labelOnTop="0" name="fid"/>
    <field labelOnTop="0" name="fid_2"/>
    <field labelOnTop="0" name="fmode"/>
    <field labelOnTop="0" name="folder"/>
    <field labelOnTop="0" name="grb"/>
    <field labelOnTop="0" name="grbMod"/>
    <field labelOnTop="0" name="grbmod"/>
    <field labelOnTop="0" name="icon"/>
    <field labelOnTop="0" name="id"/>
    <field labelOnTop="0" name="label"/>
    <field labelOnTop="0" name="layer"/>
    <field labelOnTop="0" name="len"/>
    <field labelOnTop="0" name="lenTot"/>
    <field labelOnTop="0" name="lentot"/>
    <field labelOnTop="0" name="path"/>
    <field labelOnTop="0" name="ratio"/>
    <field labelOnTop="0" name="speed(m/s)"/>
    <field labelOnTop="0" name="status"/>
    <field labelOnTop="0" name="tessellate"/>
    <field labelOnTop="0" name="timestamp"/>
    <field labelOnTop="0" name="type"/>
    <field labelOnTop="0" name="visibility"/>
  </labelOnTop>
  <reuseLastValue>
    <field name="ELEV_MAX" reuseLastValue="0"/>
    <field name="ELEV_MIN" reuseLastValue="0"/>
    <field name="ID" reuseLastValue="0"/>
    <field name="Name" reuseLastValue="0"/>
    <field name="VALUE" reuseLastValue="0"/>
    <field name="altitude" reuseLastValue="0"/>
    <field name="altitudeMode" reuseLastValue="0"/>
    <field name="area" reuseLastValue="0"/>
    <field name="areaTot" reuseLastValue="0"/>
    <field name="begin" reuseLastValue="0"/>
    <field name="cv" reuseLastValue="0"/>
    <field name="cvMod" reuseLastValue="0"/>
    <field name="cvmod" reuseLastValue="0"/>
    <field name="deliveries" reuseLastValue="0"/>
    <field name="description" reuseLastValue="0"/>
    <field name="display" reuseLastValue="0"/>
    <field name="drawOrder" reuseLastValue="0"/>
    <field name="elev_max" reuseLastValue="0"/>
    <field name="elev_min" reuseLastValue="0"/>
    <field name="end" reuseLastValue="0"/>
    <field name="extrude" reuseLastValue="0"/>
    <field name="fg" reuseLastValue="0"/>
    <field name="fgMod" reuseLastValue="0"/>
    <field name="fgmod" reuseLastValue="0"/>
    <field name="fid" reuseLastValue="0"/>
    <field name="fid_2" reuseLastValue="0"/>
    <field name="fmode" reuseLastValue="0"/>
    <field name="folder" reuseLastValue="0"/>
    <field name="grb" reuseLastValue="0"/>
    <field name="grbMod" reuseLastValue="0"/>
    <field name="grbmod" reuseLastValue="0"/>
    <field name="icon" reuseLastValue="0"/>
    <field name="id" reuseLastValue="0"/>
    <field name="label" reuseLastValue="0"/>
    <field name="layer" reuseLastValue="0"/>
    <field name="len" reuseLastValue="0"/>
    <field name="lenTot" reuseLastValue="0"/>
    <field name="lentot" reuseLastValue="0"/>
    <field name="path" reuseLastValue="0"/>
    <field name="ratio" reuseLastValue="0"/>
    <field name="speed(m/s)" reuseLastValue="0"/>
    <field name="status" reuseLastValue="0"/>
    <field name="tessellate" reuseLastValue="0"/>
    <field name="timestamp" reuseLastValue="0"/>
    <field name="type" reuseLastValue="0"/>
    <field name="visibility" reuseLastValue="0"/>
  </reuseLastValue>
  <dataDefinedFieldProperties/>
  <widgets/>
  <previewExpression>"fid"</previewExpression>
  <mapTip enabled="1"></mapTip>
  <layerGeometryType>2</layerGeometryType>
</qgis>
